import {
  Component,
  OnInit,
  AfterViewInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { SchedulingService } from '../../core/services/scheduling.service';
import { PatientService } from '../../core/services/patient.service';
import { DentistService } from '../../core/services/dentist.service';
import { NotificationService } from '../../core/services/notification.service';
import { EmployeeService } from '../../core/services/employee.service';
import { AppointmentCategoriesService } from '../../appointment-categories/services/appointment-categories.service';
import { AppointmentFormComponent } from '../../shared/components/appointment-form/appointment-form.component';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-scheduling-form',
  templateUrl: './scheduling-form.component.html',
  styleUrls: ['./scheduling-form.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, AppointmentFormComponent],
})
export class SchedulingFormComponent
  implements OnInit, AfterViewInit, OnChanges
{
  // Inputs para integração com Schedule v2
  @Input() appointment: any = null; // Agendamento para edição
  @Input() isEditMode = false; // Modo de edição
  @Input() initialDate?: string; // Data inicial
  @Input() initialTime?: string; // Horário inicial
  @Input() initialDentistId?: number; // Dentista inicial
  @Input() useAsModal = false; // Se está sendo usado como modal/sheet

  // Outputs para comunicação com componente pai
  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();

  schedulingForm: FormGroup;
  isLoading = false;
  isSubmitting = false;
  schedulingId: number | null = null;
  patients: any[] = [];
  dentists: any[] = [];
  employees: any[] = [];
  appointmentCategories: any[] = [];
  filteredPatients: any[] = [];
  patientSearchTerm = '';
  isNewPatient = false; // Indica se é um paciente novo
  showPatientDropdown = false; // Controla a exibição do dropdown

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private schedulingService: SchedulingService,
    private patientService: PatientService,
    private dentistService: DentistService,

    private notificationService: NotificationService,
    private employeeService: EmployeeService,
    private appointmentCategoriesService: AppointmentCategoriesService
  ) {
    this.schedulingForm = this.fb.group({
      patientId: [''], // Removido required para permitir pacientes novos
      dentistId: [''], // Removido Validators.required para tornar opcional
      date: ['', Validators.required],
      time: ['', Validators.required],
      endTime: [''], // Campo para horário final
      duration: [''], // Campo para duração em minutos
      status: ['unconfirmed'], // Status fixo: "Não Iniciado"
      notes: [''],

      email: [''], // Campo de email
      phone: [''], // Campo de celular
      isFirstAppointment: [false], // Campo para primeira consulta
      procedureIds: [[]], // Campo para procedimentos selecionados
      scheduledBy: [''], // Campo para quem agendou
      scheduledAt: [new Date().toISOString().split('T')[0]], // Data atual
      appointmentCategoryId: [''], // Campo para categoria de agendamento
    });
  }

  ngOnInit(): void {
    this.isLoading = true;

    // Se não está sendo usado como modal, verificar parâmetros da rota
    if (!this.useAsModal) {
      const id = this.route.snapshot.paramMap.get('id');
      this.isEditMode = !!id;

      if (this.isEditMode && id) {
        this.schedulingId = +id;
      }
    }

    this.loadInitialData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['appointment'] && this.appointment) {
      this.populateFormWithAppointment(this.appointment);
    }
  }

  private loadInitialData(): void {
    // Carregar dados iniciais (pacientes, dentistas, planos de tratamento e, se for edição, o agendamento)
    const patients$ = this.patientService.getAllPatients().pipe(
      catchError((error) => {
        console.error('Erro ao carregar pacientes:', error);
        this.notificationService.error(
          'Erro ao carregar pacientes. Por favor, tente novamente.'
        );
        return of([]);
      })
    );

    const dentists$ = this.dentistService.getAllDentists().pipe(
      catchError((error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.notificationService.error(
          'Erro ao carregar dentistas. Por favor, tente novamente.'
        );
        return of([]);
      })
    );

    const employees$ = this.employeeService.getAllEmployees().pipe(
      catchError((error) => {
        console.error('Erro ao carregar funcionários:', error);
        this.notificationService.error(
          'Erro ao carregar funcionários. Por favor, tente novamente.'
        );
        return of([]);
      })
    );

    const appointmentCategories$ = this.appointmentCategoriesService
      .getAllCategories({ isActive: true })
      .pipe(
        catchError((error) => {
          console.error('Erro ao carregar categorias de agendamento:', error);
          this.notificationService.error(
            'Erro ao carregar categorias de agendamento. Por favor, tente novamente.'
          );
          return of({ data: [], total: 0 });
        })
      );

    const scheduling$ =
      this.isEditMode && this.schedulingId
        ? this.schedulingService.getSchedulingById(this.schedulingId).pipe(
            catchError((error) => {
              console.error('Erro ao carregar agendamento:', error);
              this.notificationService.error(
                'Erro ao carregar agendamento. Por favor, tente novamente.'
              );
              return of(null);
            })
          )
        : of(null);

    // Carregar dados em paralelo
    forkJoin({
      patients: patients$,
      dentists: dentists$,
      employees: employees$,
      appointmentCategories: appointmentCategories$,
      scheduling: scheduling$,
    }).subscribe({
      next: (result) => {
        this.patients = result.patients;
        this.filteredPatients = []; // Inicializa como array vazio para não mostrar todos os pacientes
        this.dentists = result.dentists;
        this.employees = result.employees;
        this.appointmentCategories = result.appointmentCategories.data;

        // Se estiver no modo de edição, preencher o formulário
        if (this.isEditMode && result.scheduling) {
          this.populateForm(result.scheduling);
        } else if (this.appointment) {
          // Se há um appointment passado como input, usar ele
          this.populateFormWithAppointment(this.appointment);
        } else if (!this.isEditMode && !this.useAsModal) {
          // Se for um novo agendamento na rota, verificar parâmetros da query string
          const patientId = this.route.snapshot.queryParamMap.get('patientId');
          const date = this.route.snapshot.queryParamMap.get('date');
          const time = this.route.snapshot.queryParamMap.get('time');
          // Aplicar os parâmetros ao formulário
          if (patientId) {
            this.schedulingForm.patchValue({ patientId: +patientId });
          }

          if (date) {
            this.schedulingForm.patchValue({ date });
          }

          if (time) {
            this.schedulingForm.patchValue({ time });
          }
        }

        // Aplicar valores iniciais se fornecidos
        this.applyInitialValues();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar dados:', error);
        this.notificationService.error(
          'Erro ao carregar dados. Por favor, tente novamente.'
        );
        this.isLoading = false;
      },
    });
  }

  private applyInitialValues(): void {
    const updates: any = {};

    if (this.initialDate) {
      updates.date = this.initialDate;
    }

    if (this.initialTime) {
      updates.time = this.initialTime;
    }

    if (this.initialDentistId) {
      updates.dentistId = this.initialDentistId;
    }

    if (Object.keys(updates).length > 0) {
      this.schedulingForm.patchValue(updates);
    }
  }

  private populateFormWithAppointment(appointment: any): void {
    const date = new Date(appointment.date);
    const formattedDate = date.toISOString().split('T')[0];

    // Encontrar o paciente para exibir o nome no campo de busca
    const patient = this.patients.find((p) => p.id === appointment.patientId);
    if (patient) {
      this.patientSearchTerm = patient.name;
    }

    this.schedulingForm.patchValue({
      patientId: Number(appointment.patientId),
      dentistId: appointment.dentistId ? Number(appointment.dentistId) : null,
      date: formattedDate,
      time: appointment.startTime,
      endTime: appointment.endTime || '',
      duration: appointment.duration || '',
      notes: appointment.notes || '',
      appointmentCategoryId: appointment.appointmentCategory?.id || '',
    });
  }

  populateForm(scheduling: any): void {
    // Formatar a data para o formato esperado pelo input type="date"
    const date = new Date(scheduling.date);
    const formattedDate = date.toISOString().split('T')[0];

    // Encontrar o paciente para exibir o nome no campo de busca
    const patient = this.patients.find((p) => p.id === scheduling.patientId);
    if (patient) {
      this.patientSearchTerm = patient.name;
    } else {
      // Se não encontrar o paciente na lista atual, buscar diretamente
      this.patientService.getPatient(scheduling.patientId).subscribe({
        next: (patient) => {
          if (patient) {
            this.patientSearchTerm = patient.name;
          }
        },
        error: (error) => {
          console.error('Erro ao carregar dados do paciente:', error);
        },
      });
    }

    // Formatar a data de criação para o formato esperado pelo input type="date"
    const createdAtDate = scheduling.createdAt
      ? new Date(scheduling.createdAt)
      : new Date();
    const formattedCreatedAt = createdAtDate.toISOString().split('T')[0];

    this.schedulingForm.patchValue({
      patientId: Number(scheduling.patientId),
      dentistId: scheduling.dentistId ? Number(scheduling.dentistId) : null,
      date: formattedDate,
      time: scheduling.time,
      endTime: scheduling.endTime || '',
      duration: scheduling.duration || '',
      notes: scheduling.notes,

      scheduledBy: scheduling.scheduledBy?.id || '',
      scheduledAt: formattedCreatedAt,
      appointmentCategoryId: scheduling.appointmentCategory?.id || '',
    });
  }

  ngAfterViewInit(): void {
    // Desabilitar o campo scheduledAt após a inicialização
    this.schedulingForm.get('scheduledAt')?.disable();
  }

  onSubmit(): void {
    // Validação customizada para paciente
    if (!this.isPatientValid()) {
      this.schedulingForm.get('patientId')?.markAsTouched();
      return;
    }

    if (this.schedulingForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.schedulingForm.controls).forEach((key) => {
        const control = this.schedulingForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValues = this.schedulingForm.value;

    // Se está sendo usado como modal, emitir os dados para o componente pai
    if (this.useAsModal) {
      if (this.isNewPatient) {
        this.createPatientAndEmitData(formValues);
      } else {
        this.emitFormData(formValues);
      }
    } else {
      // Se é um paciente novo, criar o paciente primeiro
      if (this.isNewPatient) {
        this.createPatientAndScheduling(formValues);
      } else {
        // Paciente existente, criar agendamento diretamente
        this.createScheduling(formValues);
      }
    }
  }

  private createPatientAndScheduling(formValues: any): void {
    // Primeiro, criar o paciente usando o endpoint para pacientes externos
    const patientData = {
      name: this.patientSearchTerm.trim(),
      phone: formValues.phone || '',
    };

    this.patientService.createExternalPatient(patientData).subscribe({
      next: (newPatient) => {
        console.log('Paciente criado com ID:', newPatient.id);

        // Agora criar o agendamento com o ID do paciente
        const schedulingData = this.prepareSchedulingData(
          formValues,
          newPatient.id
        );
        this.createSchedulingWithPatientId(schedulingData);
      },
      error: (error) => {
        console.error('Erro ao criar paciente:', error);
        this.notificationService.error(
          'Erro ao criar paciente. Por favor, tente novamente.'
        );
        this.isSubmitting = false;
      },
    });
  }

  private createScheduling(formValues: any): void {
    const schedulingData = this.prepareSchedulingData(
      formValues,
      formValues.patientId
    );
    this.createSchedulingWithPatientId(schedulingData);
  }

  private prepareSchedulingData(formValues: any, patientId: number): any {
    return {
      ...formValues,
      // Usar o ID do paciente (criado ou existente)
      patientId: Number(patientId),
      dentistId:
        formValues.dentistId && formValues.dentistId !== ''
          ? Number(formValues.dentistId)
          : null,
      appointmentCategoryId:
        formValues.appointmentCategoryId &&
        formValues.appointmentCategoryId !== ''
          ? Number(formValues.appointmentCategoryId)
          : null,
      // Incluir duração como número
      duration: formValues.duration ? Number(formValues.duration) : null,
      // Ajustar a data para o fuso horário correto
      date: this.adjustDateForTimezone(formValues.date),
      // Remover campos que não são necessários para o agendamento
      patientName: undefined,
      isNewPatient: undefined,
    };
  }

  private createSchedulingWithPatientId(schedulingData: any): void {
    const operation = this.isEditMode
      ? this.schedulingService.updateScheduling(
          this.schedulingId!,
          schedulingData
        )
      : this.schedulingService.createScheduling(schedulingData);

    operation.subscribe({
      next: () => {
        const message = this.isEditMode
          ? 'Agendamento atualizado com sucesso!'
          : 'Agendamento criado com sucesso!';
        this.notificationService.success(message);
        this.isSubmitting = false;
        this.router.navigate(['/schedulings']);
      },
      error: (error) => {
        console.error('Erro ao salvar agendamento:', error);
        this.notificationService.error(
          'Erro ao salvar agendamento. Por favor, tente novamente.'
        );
        this.isSubmitting = false;
      },
    });
  }

  private createPatientAndEmitData(formValues: any): void {
    const patientData = {
      name: this.patientSearchTerm.trim(),
      phone: formValues.phone || '',
    };

    this.patientService.createExternalPatient(patientData).subscribe({
      next: (newPatient) => {
        const schedulingData = this.prepareSchedulingData(
          formValues,
          newPatient.id
        );
        this.emitFormData(schedulingData);
      },
      error: (error) => {
        console.error('Erro ao criar paciente:', error);
        this.notificationService.error(
          'Erro ao criar paciente. Por favor, tente novamente.'
        );
        this.isSubmitting = false;
      },
    });
  }

  private emitFormData(data: any): void {
    this.formSubmit.emit(data);
    this.isSubmitting = false;
  }

  onFormSubmit(formData: any): void {
    if (this.useAsModal) {
      this.formSubmit.emit(formData);
    } else {
      // Lógica original para criar/atualizar agendamento
      this.createScheduling(formData);
    }
  }

  cancel(): void {
    if (this.useAsModal) {
      this.formCancel.emit();
    } else {
      if (this.isEditMode && this.schedulingId) {
        this.router.navigate(['/schedulings', this.schedulingId]);
      } else {
        this.router.navigate(['/schedulings']);
      }
    }
  }

  filterPatients(): void {
    // Não filtrar pacientes no modo de edição
    if (this.isEditMode) {
      this.filteredPatients = [];
      this.showPatientDropdown = false;
      return;
    }

    // Se o campo estiver vazio ou com menos de 3 caracteres, limpar estado
    if (this.patientSearchTerm.length < 3) {
      this.filteredPatients = [];
      this.showPatientDropdown = false;
      this.isNewPatient = false; // Remover tag "Novo" quando limpar o campo
      this.schedulingForm.patchValue({ patientId: '' }); // Limpar patientId
      return;
    }

    const searchTerm = this.patientSearchTerm.toLowerCase();
    this.filteredPatients = this.patients.filter(
      (patient) =>
        patient.name.toLowerCase().includes(searchTerm) ||
        (patient.cpf && patient.cpf.includes(searchTerm))
    );

    this.showPatientDropdown = true;
  }

  selectPatient(patient: any): void {
    // Garantir que o patientId seja um número
    this.schedulingForm.patchValue({
      patientId: Number(patient.id),
      email: patient.email || '', // Preencher email do paciente
      phone: patient.phone || '', // Preencher telefone do paciente
    });
    this.patientSearchTerm = patient.name;
    this.filteredPatients = [];
    this.showPatientDropdown = false;
    this.isNewPatient = false; // Resetar flag de novo paciente
  }

  selectNewPatient(): void {
    // Marcar como novo paciente
    this.isNewPatient = true;
    this.filteredPatients = [];
    this.showPatientDropdown = false;

    // Limpar o patientId para indicar que é um novo paciente
    this.schedulingForm.patchValue({
      patientId: null,
    });
  }

  onPatientInputFocus(): void {
    if (!this.isEditMode && this.patientSearchTerm.length >= 3) {
      this.showPatientDropdown = true;
    }
  }

  onPatientInputBlur(): void {
    // Delay para permitir clique nos itens do dropdown
    setTimeout(() => {
      this.showPatientDropdown = false;
    }, 200);
  }

  getSelectedPatientName(): string {
    const patientId = this.schedulingForm.get('patientId')?.value;
    if (!patientId) return '';

    // Converter para número para garantir a comparação correta
    const patient = this.patients.find((p) => p.id === Number(patientId));
    return patient ? patient.name : '';
  }

  // Método para ajustar a data para o fuso horário correto
  adjustDateForTimezone(dateString: string): string {
    // Simplesmente retornar a data no formato YYYY-MM-DD sem ajustes de fuso horário
    // Isso garante que a data exibida na tela seja a mesma que foi selecionada pelo usuário
    return dateString;
  }

  // Método chamado quando um horário é selecionado
  onTimeSelected(timeSelection: { startTime: string; endTime: string }): void {
    // Calcular a duração em minutos
    const duration = this.calculateDuration(
      timeSelection.startTime,
      timeSelection.endTime
    );

    // Atualizar o formulário com todos os dados de horário
    this.schedulingForm.patchValue({
      time: timeSelection.startTime,
      endTime: timeSelection.endTime,
      duration: duration,
    });
  }

  // Método para calcular a duração entre dois horários
  private calculateDuration(startTime: string, endTime: string): number {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    return endMinutes - startMinutes;
  }

  // Método para converter horário em minutos
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Getter para facilitar acesso aos controles do formulário
  get dentistIdControl() {
    return this.schedulingForm.get('dentistId');
  }

  get dateControl() {
    return this.schedulingForm.get('date');
  }

  get timeControl() {
    return this.schedulingForm.get('time');
  }

  // Validação customizada para paciente
  isPatientValid(): boolean {
    return this.isNewPatient
      ? this.patientSearchTerm.trim().length >= 3
      : !!this.schedulingForm.get('patientId')?.value;
  }
}
